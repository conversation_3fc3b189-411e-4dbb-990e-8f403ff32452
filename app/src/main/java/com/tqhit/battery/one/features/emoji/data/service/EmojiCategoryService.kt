package com.tqhit.battery.one.features.emoji.data.service

import com.google.gson.Gson
import com.tqhit.adlib.sdk.firebase.FirebaseRemoteConfigHelper
import com.tqhit.battery.one.features.emoji.domain.model.EmojiCategory
import com.tqhit.battery.one.utils.BatteryLogger
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Service responsible for fetching and parsing emoji category data from Firebase Remote Config.
 * 
 * Following SOLID principles and established app patterns:
 * - Single Responsibility: Only handles emoji category data fetching and parsing
 * - Open/Closed: Extensible for different data sources
 * - Dependency Inversion: Depends on abstractions (FirebaseRemoteConfigHelper)
 * - Follows AnimationDataService pattern for consistency
 */
@Singleton
class EmojiCategoryService @Inject constructor(
    private val remoteConfigHelper: FirebaseRemoteConfigHelper,
    private val gson: Gson
) {
    companion object {
        private const val TAG = "EmojiCategoryService"
        private const val EMOJI_CATEGORIES_KEY = "emoji_categories"
    }
    
    /**
     * Fetches emoji categories from Firebase Remote Config with fallback to defaults.
     * Returns only categories with status=true, sorted by priority.
     * 
     * @return List of valid, enabled emoji categories sorted by priority
     */
    suspend fun getEmojiCategories(): List<EmojiCategory> = withContext(Dispatchers.IO) {
        try {
            BatteryLogger.d(TAG, "REMOTE_CONFIG: Fetching emoji categories from remote config")
            
            val jsonString = remoteConfigHelper.getString(EMOJI_CATEGORIES_KEY)
            BatteryLogger.d(TAG, "REMOTE_CONFIG: Retrieved JSON string length: ${jsonString.length}")
            
            if (jsonString.isBlank()) {
                BatteryLogger.w(TAG, "REMOTE_CONFIG: Empty JSON string, using fallback categories")
                return@withContext createFallbackCategories()
            }
            
            val categories = parseEmojiCategories(jsonString)
            val validCategories = filterAndSortCategories(categories)
            
            BatteryLogger.d(TAG, "REMOTE_CONFIG: Successfully loaded ${validCategories.size} valid categories")
            return@withContext validCategories
            
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "REMOTE_CONFIG: Error fetching emoji categories", e)
            return@withContext createFallbackCategories()
        }
    }
    
    /**
     * Parses JSON string into list of EmojiCategory objects.
     * Handles parsing errors gracefully.
     * 
     * @param jsonString JSON string from remote config
     * @return List of parsed categories, empty list if parsing fails
     */
    private fun parseEmojiCategories(jsonString: String): List<EmojiCategory> {
        return try {
            val categories = gson.fromJson(jsonString, Array<EmojiCategory>::class.java).toList()
            BatteryLogger.d(TAG, "REMOTE_CONFIG: Successfully parsed ${categories.size} categories")
            categories
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "REMOTE_CONFIG: Error parsing emoji categories JSON", e)
            emptyList()
        }
    }
    
    /**
     * Filters categories to only include valid and enabled ones, then sorts by priority.
     * 
     * @param categories Raw list of categories from parsing
     * @return Filtered and sorted list of valid categories
     */
    private fun filterAndSortCategories(categories: List<EmojiCategory>): List<EmojiCategory> {
        val validCategories = categories.filter { category ->
            val isValid = category.isValid() && category.status
            if (!isValid) {
                BatteryLogger.w(TAG, "REMOTE_CONFIG: Filtering out invalid/disabled category: ${category.id}")
            }
            isValid
        }.sortedBy { it.priority }
        
        BatteryLogger.d(TAG, "REMOTE_CONFIG: Filtered to ${validCategories.size} valid categories")
        return validCategories
    }
    
    /**
     * Creates fallback categories when remote config fails or returns empty data.
     *
     * Note: If Firebase Remote Config is properly initialized with setDefaultsAsync(R.xml.remote_config_defaults),
     * then getString() should automatically return values from remote_config_defaults.xml when remote fetch fails.
     * This method provides additional fallback for cases where defaults are not properly initialized.
     *
     * @return List of fallback emoji categories
     */
    private fun createFallbackCategories(): List<EmojiCategory> {
        BatteryLogger.d(TAG, "REMOTE_CONFIG: Creating fallback categories")
        BatteryLogger.w(TAG, "REMOTE_CONFIG: Using hardcoded fallback - ensure remote_config_defaults.xml is properly initialized")

        return createMinimalFallbackCategories()
    }

    /**
     * Creates minimal fallback categories as last resort when XML defaults are not available.
     *
     * @return List of minimal emoji categories
     */
    private fun createMinimalFallbackCategories(): List<EmojiCategory> {
        BatteryLogger.d(TAG, "REMOTE_CONFIG: Creating minimal fallback categories")

        return listOf(
            EmojiCategory("hot_category", 1, "🔥 HOT", true, false),
            EmojiCategory("character_category", 3, "Character", true, false),
            EmojiCategory("animal_category", 8, "Animal", true, false)
        )
    }
    
    /**
     * Checks if emoji category data is available from remote config.
     * Useful for debugging and monitoring.
     * 
     * @return true if remote config contains emoji category data
     */
    suspend fun isEmojiCategoryDataAvailable(): Boolean = withContext(Dispatchers.IO) {
        try {
            val jsonString = remoteConfigHelper.getString(EMOJI_CATEGORIES_KEY)
            val isAvailable = jsonString.isNotBlank()
            BatteryLogger.d(TAG, "REMOTE_CONFIG: Emoji category data available: $isAvailable")
            isAvailable
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "REMOTE_CONFIG: Error checking emoji category data availability", e)
            false
        }
    }
}
