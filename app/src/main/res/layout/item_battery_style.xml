<?xml version="1.0" encoding="utf-8"?>
<com.tqhit.battery.one.ui.custom.SquareCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="@dimen/_4sdp"
    android:focusable="true"
    android:clickable="true"
    android:foreground="?android:attr/selectableItemBackground"
    app:cardBackgroundColor="?attr/grey_lighter"
    app:cardCornerRadius="12dp"
    app:cardElevation="2dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:padding="@dimen/_8sdp">

    <!-- Main Style Image -->
    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/styleImage"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_margin="@dimen/_8sdp"
        android:adjustViewBounds="true"
        android:background="?attr/black"
        android:scaleType="centerCrop"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:shapeAppearanceOverlay="@style/rounded_corner" />

    <!-- Shimmer Loading Effect -->
    <com.facebook.shimmer.ShimmerFrameLayout
        android:id="@+id/shimmerLayout"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_margin="@dimen/_8sdp"
        android:background="?attr/grey"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent" />

    <!-- Premium Lock Indicator -->
    <TextView
        android:id="@+id/lockBtn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/premium_block_up_right"
        android:paddingLeft="@dimen/_10sdp"
        android:paddingTop="@dimen/_6sdp"
        android:paddingRight="@dimen/_10sdp"
        android:paddingBottom="@dimen/_6sdp"
        android:text="💎"
        android:textColor="@android:color/white"
        android:textSize="@dimen/_8ssp"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="@id/styleImage"
        app:layout_constraintEnd_toEndOf="@id/styleImage"
        android:layout_marginTop="@dimen/_8sdp"
        android:layout_marginEnd="@dimen/_8sdp" />

    <!-- Style Name - Hidden for gallery display -->
    <TextView
        android:id="@+id/styleName"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_4sdp"
        android:layout_marginEnd="@dimen/_4sdp"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="2"
        android:text="Style Name"
        android:textColor="?attr/black"
        android:textSize="@dimen/_12ssp"
        android:textStyle="bold"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/styleImage" />

    <!-- Popular Indicator (for HOT category) -->
    <ImageView
        android:id="@+id/popularIndicator"
        android:layout_width="@dimen/_24sdp"
        android:layout_height="@dimen/_24sdp"
        android:layout_marginStart="@dimen/_8sdp"
        android:layout_marginTop="@dimen/_8sdp"
        android:src="@drawable/ic_fire"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="@id/styleImage"
        app:layout_constraintTop_toTopOf="@id/styleImage" />

    <!-- Category Badge -->
    <TextView
        android:id="@+id/categoryBadge"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_8sdp"
        android:layout_marginBottom="@dimen/_8sdp"
        android:background="@drawable/category_badge_background"
        android:paddingStart="@dimen/_6sdp"
        android:paddingTop="@dimen/_2sdp"
        android:paddingEnd="@dimen/_6sdp"
        android:paddingBottom="@dimen/_2sdp"
        android:text="❤️"
        android:textColor="@android:color/white"
        android:textSize="@dimen/_8ssp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/styleImage"
        app:layout_constraintStart_toStartOf="@id/styleImage" />

    <!-- Selection Overlay (for selected state) -->
    <View
        android:id="@+id/selectionOverlay"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_margin="@dimen/_8sdp"
        android:background="@drawable/selection_overlay"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent" />

    <!-- Selection Check Mark -->
    <ImageView
        android:id="@+id/selectionCheckMark"
        android:layout_width="@dimen/_32sdp"
        android:layout_height="@dimen/_32sdp"
        android:src="@drawable/ic_check_circle"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/selectionOverlay"
        app:layout_constraintEnd_toEndOf="@id/selectionOverlay"
        app:layout_constraintStart_toStartOf="@id/selectionOverlay"
        app:layout_constraintTop_toTopOf="@id/selectionOverlay" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</com.tqhit.battery.one.ui.custom.SquareCardView>
